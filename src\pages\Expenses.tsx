import { useState } from "react";
import { 
  <PERSON>, 
  Card<PERSON>ontent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>ontent, 
  <PERSON><PERSON>List, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DataTable } from "@/components/common/DataTable";
import { MetricCard } from "@/components/common/MetricCard";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";
import { expenseService } from "@/lib/firebase";
import { Dialog, DialogContent, DialogTrigger, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { AddExpenseForm } from "@/components/expenses/AddExpenseForm";
import {
  DollarSign,
  TrendingUp,
  TrendingDown,
  Calendar,
  Plus,
  Eye,
  Edit,
  Trash2,
  Filter,
  Download,
  Receipt,
  CreditCard,
  Building,
  Zap
} from "lucide-react";
import { <PERSON><PERSON><PERSON> as RechartsBar<PERSON><PERSON>, Bar, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, ResponsiveContainer, Pie<PERSON>hart, Pie, Cell } from "recharts";
import { format, subDays, startOfMonth, endOfMonth } from "date-fns";
import type { Expense } from '@/types/firebase';
import { useAuth } from "@/context/AuthContext";
import { toast } from "sonner";
import { useRecurringExpenses } from "@/hooks/use-recurring-expenses";

export default function Expenses() {
  const { isAdmin, currentUser } = useAuth();
  const [activeTab, setActiveTab] = useState("overview");
  const [showExpenseForm, setShowExpenseForm] = useState(false);
  const [editingExpense, setEditingExpense] = useState<Expense | null>(null);
  const [viewingExpense, setViewingExpense] = useState<Expense | null>(null);

  // Initialize recurring expenses processing
  const { processRecurringExpenses } = useRecurringExpenses();

  // Handle manual recurring expense processing
  const handleProcessRecurring = async () => {
    await processRecurringExpenses();
    // Refresh the expenses list after processing
    refetchExpenses();
  };

  const { data: expensesData = [], isLoading: isLoadingExpenses, refetch: refetchExpenses } = useQuery<Expense[]>({
    queryKey: ["expenses"],
    queryFn: async () => {
      return await expenseService.getAll();
    }
  });

  const { data: categorySummary = {}, isLoading: isLoadingCategories } = useQuery({
    queryKey: ["expense-categories"],
    queryFn: async () => {
      return await expenseService.getCategorySummary();
    }
  });

  // Calculate metrics
  const currentMonth = new Date();
  const startOfCurrentMonth = startOfMonth(currentMonth);
  const endOfCurrentMonth = endOfMonth(currentMonth);
  
  const currentMonthExpenses = expensesData.filter(expense => {
    const expenseDate = new Date(expense.date);
    return expenseDate >= startOfCurrentMonth && expenseDate <= endOfCurrentMonth;
  });

  const lastMonth = subDays(currentMonth, 30);
  const startOfLastMonth = startOfMonth(lastMonth);
  const endOfLastMonth = endOfMonth(lastMonth);
  
  const lastMonthExpenses = expensesData.filter(expense => {
    const expenseDate = new Date(expense.date);
    return expenseDate >= startOfLastMonth && expenseDate <= endOfLastMonth;
  });

  const totalCurrentMonth = currentMonthExpenses.reduce((sum, expense) => sum + expense.amount, 0);
  const totalLastMonth = lastMonthExpenses.reduce((sum, expense) => sum + expense.amount, 0);
  const monthlyChange = totalLastMonth > 0 ? ((totalCurrentMonth - totalLastMonth) / totalLastMonth) * 100 : 0;

  const pendingExpenses = expensesData.filter(expense => expense.status === 'pending');
  const totalPending = pendingExpenses.reduce((sum, expense) => sum + expense.amount, 0);

  const approvedExpenses = expensesData.filter(expense => expense.status === 'approved');
  const totalApproved = approvedExpenses.reduce((sum, expense) => sum + expense.amount, 0);

  // Prepare chart data
  const categoryChartData = Object.entries(categorySummary).map(([category, data]) => ({
    name: category,
    value: data.total,
    count: data.count
  }));

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];

  // Table columns
  const expenseColumns = [
    {
      id: "title",
      header: "Title",
      cell: (expense: Expense) => (
        <div className="font-medium">{expense.title}</div>
      ),
    },
    {
      id: "category",
      header: "Category",
      cell: (expense: Expense) => (
        <Badge variant="outline">{expense.category}</Badge>
      ),
    },
    {
      id: "amount",
      header: "Amount",
      cell: (expense: Expense) => (
        <div className="font-medium">Tsh {expense.amount.toFixed(2)}</div>
      ),
    },
    {
      id: "date",
      header: "Date",
      cell: (expense: Expense) => (
        <div>{format(new Date(expense.date), "MMM dd, yyyy")}</div>
      ),
    },
    {
      id: "status",
      header: "Status",
      cell: (expense: Expense) => {
        const status = expense.status;
        return (
          <Badge
            variant={status === 'approved' ? 'default' : status === 'pending' ? 'secondary' : 'destructive'}
          >
            {status}
          </Badge>
        );
      },
    },
    {
      id: "recurring",
      header: "Type",
      cell: (expense: Expense) => (
        <div className="flex items-center gap-1">
          {expense.isRecurring ? (
            <>
              <Calendar className="h-4 w-4 text-blue-500" />
              <Badge variant="outline" className="text-blue-600 border-blue-200">
                {expense.recurringFrequency}
              </Badge>
            </>
          ) : (
            <Badge variant="outline" className="text-gray-600">
              One-time
            </Badge>
          )}
        </div>
      ),
    },
    {
      id: "paymentMethod",
      header: "Payment",
      cell: (expense: Expense) => (
        <div className="flex items-center gap-1">
          {expense.paymentMethod === 'card' && <CreditCard className="h-4 w-4" />}
          {expense.paymentMethod === 'cash' && <DollarSign className="h-4 w-4" />}
          {expense.paymentMethod === 'bank_transfer' && <Building className="h-4 w-4" />}
          <span className="capitalize">{expense.paymentMethod.replace('_', ' ')}</span>
        </div>
      ),
    },
    {
      id: "actions",
      header: "Actions",
      cell: (expense: Expense) => {
        return (
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setViewingExpense(expense)}
            >
              <Eye className="h-4 w-4" />
            </Button>
            {isAdmin() && (
              <>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setEditingExpense(expense);
                    setShowExpenseForm(true);
                  }}
                >
                  <Edit className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => handleDeleteExpense(expense.id)}
                >
                  <Trash2 className="h-4 w-4" />
                </Button>
              </>
            )}
          </div>
        );
      },
    },
  ];

  const handleDeleteExpense = async (expenseId: string) => {
    if (!confirm("Are you sure you want to delete this expense?")) return;
    
    try {
      await expenseService.delete(expenseId);
      toast.success("Expense deleted successfully");
      refetchExpenses();
    } catch (error) {
      toast.error("Failed to delete expense");
      console.error("Error deleting expense:", error);
    }
  };

  const handleExpenseAdded = () => {
    refetchExpenses();
    setShowExpenseForm(false);
    setEditingExpense(null);
  };

  return (
    <div className="space-y-4 sm:space-y-6 animate-fade-in">
      <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Expense Management</h1>
          <p className="text-muted-foreground text-sm sm:text-base">
            Track and manage your business expenses and financial calculations.
          </p>
        </div>
        {isAdmin() && (
          <Dialog open={showExpenseForm} onOpenChange={setShowExpenseForm}>
            <DialogTrigger asChild>
              <Button className="w-full sm:w-auto">
                <Plus className="mr-2 h-4 w-4" />
                <span className="sm:hidden">Add Expense</span>
                <span className="hidden sm:inline">Add Expense</span>
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[90vw] md:max-w-[700px] max-h-[90vh] overflow-y-auto">
              <DialogHeader>
                <DialogTitle>{editingExpense ? 'Edit Expense' : 'Add New Expense'}</DialogTitle>
              </DialogHeader>
              <AddExpenseForm
                expense={editingExpense}
                onClose={() => {
                  setShowExpenseForm(false);
                  setEditingExpense(null);
                }}
                onExpenseAdded={handleExpenseAdded}
              />
            </DialogContent>
          </Dialog>
        )}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="expenses">All Expenses</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <MetricCard
              title="This Month"
              value={`Tsh ${totalCurrentMonth.toFixed(2)}`}
              icon={<DollarSign className="h-4 w-4" />}
              trend={{ value: Math.abs(monthlyChange), positive: monthlyChange <= 0 }}
            />
            <MetricCard
              title="Pending Approval"
              value={`Tsh ${totalPending.toFixed(2)}`}
              icon={<Calendar className="h-4 w-4" />}
              description={`${pendingExpenses.length} expenses`}
            />
            <MetricCard
              title="Approved Total"
              value={`Tsh ${totalApproved.toFixed(2)}`}
              icon={<Receipt className="h-4 w-4" />}
              description={`${approvedExpenses.length} expenses`}
            />
            <MetricCard
              title="Categories"
              value={Object.keys(categorySummary).length.toString()}
              icon={<Filter className="h-4 w-4" />}
              description="Active categories"
            />
          </div>
          
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Expense Categories</CardTitle>
                <CardDescription>Breakdown by category</CardDescription>
              </CardHeader>
              <CardContent>
                <ResponsiveContainer width="100%" height={300}>
                  <PieChart>
                    <Pie
                      data={categoryChartData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="value"
                    >
                      {categoryChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: any) => [`Tsh ${value.toFixed(2)}`, 'Amount']} />
                  </PieChart>
                </ResponsiveContainer>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Recent Expenses</CardTitle>
                <CardDescription>Latest expense entries</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {expensesData.slice(0, 5).map((expense) => (
                    <div key={expense.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{expense.title}</p>
                        <p className="text-sm text-muted-foreground">{expense.category}</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">Tsh {expense.amount.toFixed(2)}</p>
                        <Badge 
                          variant={expense.status === 'approved' ? 'default' : expense.status === 'pending' ? 'secondary' : 'destructive'}
                          className="text-xs"
                        >
                          {expense.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="expenses" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
              <div>
                <CardTitle className="text-lg sm:text-xl">All Expenses</CardTitle>
                <CardDescription>Complete list of all expense records</CardDescription>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  Export
                </Button>
                {isAdmin() && (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleProcessRecurring}
                      title="Process recurring expenses manually"
                    >
                      <Calendar className="mr-2 h-4 w-4" />
                      Process Recurring
                    </Button>
                    <Button
                      onClick={() => setShowExpenseForm(true)}
                      size="sm"
                      className="w-full sm:w-auto"
                    >
                      <Plus className="mr-2 h-4 w-4" />
                      <span className="sm:hidden">Add Expense</span>
                      <span className="hidden sm:inline">Add Expense</span>
                    </Button>
                  </>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <DataTable
                data={expensesData || []}
                columns={expenseColumns}
                isLoading={isLoadingExpenses}
                emptyMessage="No expense records found"
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Monthly Trend</CardTitle>
                <CardDescription>Expense trends over time</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <p className="font-medium">Current Month</p>
                      <p className="text-2xl font-bold">Tsh {totalCurrentMonth.toFixed(2)}</p>
                    </div>
                    <div className="text-right">
                      <div className={`flex items-center gap-1 ${monthlyChange <= 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {monthlyChange <= 0 ? <TrendingDown className="h-4 w-4" /> : <TrendingUp className="h-4 w-4" />}
                        <span className="text-sm font-medium">{Math.abs(monthlyChange).toFixed(1)}%</span>
                      </div>
                      <p className="text-sm text-muted-foreground">vs last month</p>
                    </div>
                  </div>

                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <p className="font-medium">Last Month</p>
                      <p className="text-2xl font-bold">Tsh {totalLastMonth.toFixed(2)}</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Category Breakdown</CardTitle>
                <CardDescription>Expenses by category</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {Object.entries(categorySummary).map(([category, data]) => (
                    <div key={category} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <p className="font-medium">{category}</p>
                        <p className="text-sm text-muted-foreground">{data.count} expenses</p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">Tsh {data.total.toFixed(2)}</p>
                        <p className="text-sm text-muted-foreground">
                          Tsh {(data.total / data.count).toFixed(2)} avg
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* View Expense Dialog */}
      <Dialog open={!!viewingExpense} onOpenChange={() => setViewingExpense(null)}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Expense Details</DialogTitle>
          </DialogHeader>
          {viewingExpense && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Title</label>
                  <p className="font-medium">{viewingExpense.title}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Amount</label>
                  <p className="font-medium">Tsh {viewingExpense.amount.toFixed(2)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Category</label>
                  <p className="font-medium">{viewingExpense.category}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Date</label>
                  <p className="font-medium">{format(new Date(viewingExpense.date), "MMM dd, yyyy")}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Payment Method</label>
                  <p className="font-medium capitalize">{viewingExpense.paymentMethod.replace('_', ' ')}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Status</label>
                  <Badge
                    variant={viewingExpense.status === 'approved' ? 'default' : viewingExpense.status === 'pending' ? 'secondary' : 'destructive'}
                  >
                    {viewingExpense.status}
                  </Badge>
                </div>
              </div>
              {viewingExpense.description && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Description</label>
                  <p className="font-medium">{viewingExpense.description}</p>
                </div>
              )}
              {viewingExpense.vendor && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Vendor</label>
                  <p className="font-medium">{viewingExpense.vendor}</p>
                </div>
              )}
              {viewingExpense.receiptNumber && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Receipt Number</label>
                  <p className="font-medium">{viewingExpense.receiptNumber}</p>
                </div>
              )}
              {viewingExpense.tags && viewingExpense.tags.length > 0 && (
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Tags</label>
                  <div className="flex gap-1 flex-wrap">
                    {viewingExpense.tags.map((tag, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
